"use client";

import { useState, useEffect } from "react";
import { Plus, MoreHorizontal, ChevronDown, Lock, Globe, Menu, User, LogOut } from "lucide-react";
import { useSession, signOut } from "next-auth/react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { webSocketClient, ConnectionStatus } from "@/lib/websocket-client";


interface TopBarProps {
  onToggleSidebar: () => void;
}

interface ChatModel {
  id: string;
  name: string;
  description: string;
}

export default function TopBar({ onToggleSidebar }: TopBarProps) {
  const { data: session } = useSession();
  const [selectedModel, setSelectedModel] = useState("Chat model");
  const [isPrivate, setIsPrivate] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');

  // Listen to WebSocket connection status
  useEffect(() => {
    const unsubscribe = webSocketClient.onStatusChange((status) => {
      setConnectionStatus(status);
    });

    // Get initial status
    setConnectionStatus(webSocketClient.getConnectionStatus());

    return unsubscribe;
  }, []);



  const chatModels: ChatModel[] = [
    { id: "gpt-4", name: "GPT-4", description: "Most capable model" },
    { id: "gpt-3.5", name: "GPT-3.5 Turbo", description: "Fast and efficient" },
    { id: "claude", name: "Claude", description: "Anthropic's AI assistant" },
  ];

  return (
    <div className="bg-background border-b border-border">
      <div className="flex items-center justify-between px-3 sm:px-4 py-3">
        {/* Left Section */}
        <div className="flex items-center gap-3">
          {/* Mobile Menu Toggle */}
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggleSidebar}
            className="lg:hidden h-9 w-9"
            title="Toggle Sidebar"
          >
            <Menu size={18} className="text-muted-foreground" />
          </Button>

          {/* Title */}
          <h1 className="text-base sm:text-lg font-semibold text-foreground">
            Finity Chat
          </h1>

          {/* New Chat Button */}
          <Button
            variant="ghost"
            size="icon"
            className="h-9 w-9"
            title="New Chat"
          >
            <Plus size={18} className="text-muted-foreground" />
          </Button>

          {/* Menu Button */}
          <Button
            variant="ghost"
            size="icon"
            className="h-9 w-9"
            title="Menu"
          >
            <MoreHorizontal size={18} className="text-muted-foreground" />
          </Button>
        </div>

        {/* Center Section */}
        <div className="hidden sm:flex items-center gap-4">
          {/* Chat Model Selector */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="flex items-center gap-2 h-8 px-3 text-xs"
              >
                <span className="font-medium">
                  {selectedModel}
                </span>
                <ChevronDown size={14} className="text-muted-foreground" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56">
              {chatModels.map((model) => (
                <DropdownMenuItem
                  key={model.id}
                  onClick={() => setSelectedModel(model.name)}
                  className="flex flex-col items-start p-3"
                >
                  <div className="text-xs font-medium">
                    {model.name}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {model.description}
                  </div>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Private Toggle */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="flex items-center gap-2 h-8 px-3 text-xs"
              >
                {isPrivate ? (
                  <Lock size={14} className="text-muted-foreground" />
                ) : (
                  <Globe size={14} className="text-muted-foreground" />
                )}
                <span className="font-medium">
                  {isPrivate ? "Private" : "Public"}
                </span>
                <ChevronDown size={14} className="text-muted-foreground" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-64">
              <DropdownMenuItem
                onClick={() => setIsPrivate(false)}
                className="flex items-center gap-2 p-3"
              >
                <Globe size={14} className="text-muted-foreground" />
                <div className="text-left">
                  <div className="text-xs font-medium">
                    Public
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Anyone can view this chat
                  </div>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => setIsPrivate(true)}
                className="flex items-center gap-2 p-3"
              >
                <Lock size={14} className="text-muted-foreground" />
                <div className="text-left">
                  <div className="text-xs font-medium">
                    Private
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Only you can view this chat
                  </div>
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Connection Status Indicator */}
          <div className="flex items-center gap-2">
            <div
              className={`w-2 h-2 rounded-full ${
                connectionStatus === 'connected' ? 'bg-green-500' : 'bg-red-500'
              }`}
              title={connectionStatus === 'connected' ? 'Connected' : 'Disconnected'}
            />
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center gap-2">
          {/* User Profile */}
          {session?.user && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="flex items-center gap-2 h-auto p-2"
                  title="User Menu"
                >
                  {session.user.image ? (
                    <Image
                      src={session.user.image}
                      alt={session.user.name || "User"}
                      width={24}
                      height={24}
                      className="rounded-full"
                    />
                  ) : (
                    <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                      <User size={14} className="text-primary-foreground" />
                    </div>
                  )}
                  <span className="text-sm font-medium hidden sm:block">
                    {session.user.name || session.user.email}
                  </span>
                  <ChevronDown size={14} className="text-muted-foreground hidden sm:block" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-64" align="end">
                <div className="p-3 border-b border-border">
                  <div className="flex items-center gap-3">
                    {session.user.image ? (
                      <Image
                        src={session.user.image}
                        alt={session.user.name || "User"}
                        width={40}
                        height={40}
                        className="rounded-full"
                      />
                    ) : (
                      <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                        <User size={20} className="text-primary-foreground" />
                      </div>
                    )}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">
                        {session.user.name || "User"}
                      </p>
                      <p className="text-xs text-muted-foreground truncate">
                        {session.user.email}
                      </p>
                      {session.user.role && (
                        <p className="text-xs text-primary capitalize">
                          {session.user.role}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
                <DropdownMenuItem
                  onClick={() => signOut({ callbackUrl: "/auth/signin" })}
                  className="text-destructive focus:text-destructive p-3"
                >
                  <LogOut size={14} />
                  <span className="text-sm">Sign Out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>
    </div>
  );
}
