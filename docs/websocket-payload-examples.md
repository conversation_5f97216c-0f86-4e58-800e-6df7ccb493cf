# WebSocket Payload Examples

## Struktur Payload yang Diperbarui

Berikut adalah contoh-contoh payload yang dikirim ke gateway setelah improvement:

### 1. Payload Chat <PERSON> (Text Only)

```json
{
  "session_id": "session_1726567890123_abc123def",
  "user_id": "user_clm123abc456",
  "user_email": "<EMAIL>",
  "type": "chat_message",
  "timestamp": 1726567890124,
  "payload": {
    "messages": [
      {
        "role": "user",
        "content": "Halo, bagaimana kabar hari ini?",
        "parts": [
          {
            "type": "text",
            "text": "Halo, bagaimana kabar hari ini?"
          }
        ]
      }
    ]
  }
}
```

### 2. Payload Chat dengan Gambar

```json
{
  "session_id": "session_1726567890123_abc123def",
  "user_id": "user_clm123abc456",
  "user_email": "<EMAIL>",
  "type": "chat_message",
  "timestamp": 1726567890124,
  "payload": {
    "messages": [
      {
        "role": "user",
        "content": "Lihat gambar ini dan jelaskan",
        "parts": [
          {
            "type": "text",
            "text": "Lihat gambar ini dan jelaskan"
          },
          {
            "type": "image",
            "text": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
          }
        ]
      }
    ]
  }
}
```

### 3. Payload dengan Conversation History

```json
{
  "session_id": "session_1726567890123_abc123def",
  "user_id": "user_clm123abc456",
  "user_email": "<EMAIL>",
  "type": "chat_message",
  "timestamp": 1726567890456,
  "payload": {
    "messages": [
      {
        "role": "user",
        "content": "Halo, bagaimana kabar hari ini?",
        "parts": [
          {
            "type": "text",
            "text": "Halo, bagaimana kabar hari ini?"
          }
        ]
      },
      {
        "role": "assistant",
        "content": "Halo! Kabar saya baik, terima kasih. Ada yang bisa saya bantu?",
        "parts": [
          {
            "type": "text",
            "text": "Halo! Kabar saya baik, terima kasih. Ada yang bisa saya bantu?"
          }
        ]
      },
      {
        "role": "user",
        "content": "Bisakah kamu jelaskan tentang cuaca hari ini?",
        "parts": [
          {
            "type": "text",
            "text": "Bisakah kamu jelaskan tentang cuaca hari ini?"
          }
        ]
      }
    ]
  }
}
```

## Perubahan yang Dilakukan

### 1. **Struktur Request yang Diperbarui**
- Menambahkan `session_id` untuk tracking session
- Menambahkan `user_id` untuk identifikasi user
- Menambahkan `user_email` untuk konteks user
- Menghapus field `id` yang diganti dengan `session_id`

### 2. **Dukungan Message Parts**
- Mendukung `type: 'text'` dan `type: 'image'`
- Memungkinkan multiple parts dalam satu message
- Backward compatible dengan text-only messages

### 3. **User Context Integration**
- Otomatis mengambil user info dari session next-auth
- Fallback ke anonymous user jika tidak ada session
- Generate session ID unik untuk setiap chat session

## Response Format

Gateway diharapkan mengembalikan response dengan format:

```json
{
  "session_id": "session_1726567890123_abc123def",
  "type": "chat_response",
  "timestamp": 1726567890789,
  "payload": "Ini adalah response dari AI assistant..."
}
```

Atau untuk error:

```json
{
  "session_id": "session_1726567890123_abc123def",
  "type": "error",
  "timestamp": 1726567890789,
  "payload": {
    "error": "Error message here"
  }
}
```

## Penggunaan dalam Kode

### Mengirim Chat Biasa
```typescript
const userMessage: ChatMessage = {
  role: 'user',
  content: 'Hello world',
  id: `user_${Date.now()}`
};
```

### Mengirim Chat dengan Gambar
```typescript
const userMessage: ChatMessage = {
  role: 'user',
  content: 'Lihat gambar ini',
  id: `user_${Date.now()}`,
  parts: [
    {
      type: 'text',
      text: 'Lihat gambar ini'
    },
    {
      type: 'image',
      text: 'data:image/jpeg;base64,/9j/4AAQ...'
    }
  ]
};
```

## Konfigurasi

- **Gateway URL**: `wss://gate.finityhub.ai/ws`
- **Authentication**: JWT token via URL parameter atau header
- **Timeout**: 30 detik per request
- **Reconnection**: Otomatis dengan exponential backoff
