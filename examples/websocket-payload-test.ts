/**
 * Test file untuk memverifikasi struktur payload WebSocket yang baru
 */

import { WebSocketRequest, WebSocketResponse } from '../lib/websocket-client';
import { Message, MessagePart, WebSocketMessagePayload } from '../lib/schemas';

// Test 1: Payload chat sederhana
function testSimpleChatPayload() {
  const messages: Message[] = [
    {
      role: 'user',
      content: 'Halo, bagaimana kabar hari ini?',
      parts: [
        {
          type: 'text',
          text: 'Halo, bagaimana kabar hari ini?'
        }
      ]
    }
  ];

  const request: WebSocketRequest = {
    session_id: 'session_1726567890123_abc123def',
    user_id: 'user_clm123abc456',
    user_email: '<EMAIL>',
    type: 'chat_message',
    timestamp: Date.now(),
    payload: { messages }
  };

  console.log('Simple Chat Payload:');
  console.log(JSON.stringify(request, null, 2));
  return request;
}

// Test 2: Payload chat dengan gambar
function testImageChatPayload() {
  const messages: Message[] = [
    {
      role: 'user',
      content: 'Lihat gambar ini dan jelaskan',
      parts: [
        {
          type: 'text',
          text: 'Lihat gambar ini dan jelaskan'
        },
        {
          type: 'image',
          text: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...'
        }
      ]
    }
  ];

  const request: WebSocketRequest = {
    session_id: 'session_1726567890123_abc123def',
    user_id: 'user_clm123abc456',
    user_email: '<EMAIL>',
    type: 'chat_message',
    timestamp: Date.now(),
    payload: { messages }
  };

  console.log('Image Chat Payload:');
  console.log(JSON.stringify(request, null, 2));
  return request;
}

// Test 3: Payload dengan conversation history
function testConversationHistoryPayload() {
  const messages: Message[] = [
    {
      role: 'user',
      content: 'Halo, bagaimana kabar hari ini?',
      parts: [
        {
          type: 'text',
          text: 'Halo, bagaimana kabar hari ini?'
        }
      ]
    },
    {
      role: 'assistant',
      content: 'Halo! Kabar saya baik, terima kasih. Ada yang bisa saya bantu?',
      parts: [
        {
          type: 'text',
          text: 'Halo! Kabar saya baik, terima kasih. Ada yang bisa saya bantu?'
        }
      ]
    },
    {
      role: 'user',
      content: 'Bisakah kamu jelaskan tentang cuaca hari ini?',
      parts: [
        {
          type: 'text',
          text: 'Bisakah kamu jelaskan tentang cuaca hari ini?'
        }
      ]
    }
  ];

  const request: WebSocketRequest = {
    session_id: 'session_1726567890123_abc123def',
    user_id: 'user_clm123abc456',
    user_email: '<EMAIL>',
    type: 'chat_message',
    timestamp: Date.now(),
    payload: { messages }
  };

  console.log('Conversation History Payload:');
  console.log(JSON.stringify(request, null, 2));
  return request;
}

// Test 4: Response format
function testResponseFormat() {
  const successResponse: WebSocketResponse = {
    session_id: 'session_1726567890123_abc123def',
    type: 'chat_response',
    timestamp: Date.now(),
    payload: 'Ini adalah response dari AI assistant...'
  };

  const errorResponse: WebSocketResponse = {
    session_id: 'session_1726567890123_abc123def',
    type: 'error',
    timestamp: Date.now(),
    payload: {
      error: 'Error message here'
    }
  };

  console.log('Success Response:');
  console.log(JSON.stringify(successResponse, null, 2));
  
  console.log('Error Response:');
  console.log(JSON.stringify(errorResponse, null, 2));
  
  return { successResponse, errorResponse };
}

// Test 5: Validasi struktur dengan Zod
function testSchemaValidation() {
  try {
    const testPayload = testSimpleChatPayload();
    console.log('✅ Payload structure is valid');
    return true;
  } catch (error) {
    console.error('❌ Payload structure validation failed:', error);
    return false;
  }
}

// Jalankan semua test
export function runAllTests() {
  console.log('=== WebSocket Payload Structure Tests ===\n');
  
  testSimpleChatPayload();
  console.log('\n---\n');
  
  testImageChatPayload();
  console.log('\n---\n');
  
  testConversationHistoryPayload();
  console.log('\n---\n');
  
  testResponseFormat();
  console.log('\n---\n');
  
  const isValid = testSchemaValidation();
  console.log(`\n=== Test Results: ${isValid ? 'PASSED' : 'FAILED'} ===`);
  
  return isValid;
}

// Utility function untuk membuat payload dengan user context
export function createChatPayload(
  messages: Message[],
  userContext: {
    sessionId: string;
    userId: string;
    email: string;
  }
): WebSocketRequest {
  return {
    session_id: userContext.sessionId,
    user_id: userContext.userId,
    user_email: userContext.email,
    type: 'chat_message',
    timestamp: Date.now(),
    payload: { messages }
  };
}

// Utility function untuk membuat message dengan parts
export function createMessageWithParts(
  role: 'user' | 'assistant',
  content: string,
  additionalParts: MessagePart[] = []
): Message {
  const textPart: MessagePart = {
    type: 'text',
    text: content
  };

  return {
    role,
    content,
    parts: [textPart, ...additionalParts]
  };
}

// Export untuk digunakan di tempat lain
export {
  testSimpleChatPayload,
  testImageChatPayload,
  testConversationHistoryPayload,
  testResponseFormat,
  testSchemaValidation
};
