/**
 * Test sederhana untuk memverifikasi struktur payload WebSocket
 */

// Test 1: Payload chat sederhana
function testSimpleChatPayload() {
  const messages = [
    {
      role: 'user',
      content: 'Halo, bagaimana kabar hari ini?',
      parts: [
        {
          type: 'text',
          text: '<PERSON>o, bagaimana kabar hari ini?'
        }
      ]
    }
  ];

  const request = {
    session_id: 'session_1726567890123_abc123def',
    user_id: 'user_clm123abc456',
    user_email: '<EMAIL>',
    type: 'chat_message',
    timestamp: Date.now(),
    payload: { messages }
  };

  console.log('✅ Simple Chat Payload:');
  console.log(JSON.stringify(request, null, 2));
  return request;
}

// Test 2: Payload chat dengan gambar
function testImageChatPayload() {
  const messages = [
    {
      role: 'user',
      content: 'Lihat gambar ini dan jelaskan',
      parts: [
        {
          type: 'text',
          text: 'Lihat gambar ini dan jelaskan'
        },
        {
          type: 'image',
          text: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...'
        }
      ]
    }
  ];

  const request = {
    session_id: 'session_1726567890123_abc123def',
    user_id: 'user_clm123abc456',
    user_email: '<EMAIL>',
    type: 'chat_message',
    timestamp: Date.now(),
    payload: { messages }
  };

  console.log('✅ Image Chat Payload:');
  console.log(JSON.stringify(request, null, 2));
  return request;
}

// Test 3: Payload dengan conversation history
function testConversationHistoryPayload() {
  const messages = [
    {
      role: 'user',
      content: 'Halo, bagaimana kabar hari ini?',
      parts: [
        {
          type: 'text',
          text: 'Halo, bagaimana kabar hari ini?'
        }
      ]
    },
    {
      role: 'assistant',
      content: 'Halo! Kabar saya baik, terima kasih. Ada yang bisa saya bantu?',
      parts: [
        {
          type: 'text',
          text: 'Halo! Kabar saya baik, terima kasih. Ada yang bisa saya bantu?'
        }
      ]
    },
    {
      role: 'user',
      content: 'Bisakah kamu jelaskan tentang cuaca hari ini?',
      parts: [
        {
          type: 'text',
          text: 'Bisakah kamu jelaskan tentang cuaca hari ini?'
        }
      ]
    }
  ];

  const request = {
    session_id: 'session_1726567890123_abc123def',
    user_id: 'user_clm123abc456',
    user_email: '<EMAIL>',
    type: 'chat_message',
    timestamp: Date.now(),
    payload: { messages }
  };

  console.log('✅ Conversation History Payload:');
  console.log(JSON.stringify(request, null, 2));
  return request;
}

// Test 4: Response format
function testResponseFormat() {
  const successResponse = {
    session_id: 'session_1726567890123_abc123def',
    type: 'chat_response',
    timestamp: Date.now(),
    payload: 'Ini adalah response dari AI assistant...'
  };

  const errorResponse = {
    session_id: 'session_1726567890123_abc123def',
    type: 'error',
    timestamp: Date.now(),
    payload: {
      error: 'Error message here'
    }
  };

  console.log('✅ Success Response:');
  console.log(JSON.stringify(successResponse, null, 2));
  
  console.log('✅ Error Response:');
  console.log(JSON.stringify(errorResponse, null, 2));
  
  return { successResponse, errorResponse };
}

// Validasi struktur
function validatePayloadStructure(payload) {
  const requiredFields = ['session_id', 'user_id', 'user_email', 'type', 'timestamp', 'payload'];
  
  for (const field of requiredFields) {
    if (!(field in payload)) {
      throw new Error(`Missing required field: ${field}`);
    }
  }
  
  if (payload.type !== 'chat_message') {
    throw new Error(`Invalid type: ${payload.type}`);
  }
  
  if (!payload.payload.messages || !Array.isArray(payload.payload.messages)) {
    throw new Error('Invalid messages structure');
  }
  
  for (const message of payload.payload.messages) {
    if (!message.role || !message.content || !message.parts) {
      throw new Error('Invalid message structure');
    }
    
    if (!['user', 'assistant'].includes(message.role)) {
      throw new Error(`Invalid role: ${message.role}`);
    }
    
    for (const part of message.parts) {
      if (!part.type || !part.text) {
        throw new Error('Invalid message part structure');
      }
      
      if (!['text', 'image'].includes(part.type)) {
        throw new Error(`Invalid part type: ${part.type}`);
      }
    }
  }
  
  return true;
}

// Jalankan semua test
function runAllTests() {
  console.log('=== WebSocket Payload Structure Tests ===\n');
  
  try {
    const simplePayload = testSimpleChatPayload();
    validatePayloadStructure(simplePayload);
    console.log('✅ Simple payload validation passed\n');
    
    console.log('---\n');
    
    const imagePayload = testImageChatPayload();
    validatePayloadStructure(imagePayload);
    console.log('✅ Image payload validation passed\n');
    
    console.log('---\n');
    
    const historyPayload = testConversationHistoryPayload();
    validatePayloadStructure(historyPayload);
    console.log('✅ History payload validation passed\n');
    
    console.log('---\n');
    
    testResponseFormat();
    console.log('✅ Response format test passed\n');
    
    console.log('=== All Tests PASSED ===');
    return true;
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('=== Tests FAILED ===');
    return false;
  }
}

// Jalankan test
runAllTests();
