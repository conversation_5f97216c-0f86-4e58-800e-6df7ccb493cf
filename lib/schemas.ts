import { z } from 'zod';

// Message part schema for fin_gateway compatibility
export const MessagePartSchema = z.object({
  type: z.enum(['text', 'image']),
  text: z.string(),
});

// Message schema with parts structure
export const MessageSchema = z.object({
  role: z.enum(['user', 'assistant']),
  content: z.string(),
  parts: z.array(MessagePartSchema),
});

// WebSocket message payload schema for fin_gateway
export const WebSocketMessagePayloadSchema = z.object({
  messages: z.array(MessageSchema),
});

// WebSocket request schema with user context
export const WebSocketRequestSchema = z.object({
  session_id: z.string(),
  user_id: z.string(),
  user_email: z.string(),
  type: z.literal('chat_message'),
  timestamp: z.number(),
  payload: WebSocketMessagePayloadSchema,
});

export type MessagePart = z.infer<typeof MessagePartSchema>;
export type Message = z.infer<typeof MessageSchema>;
export type WebSocketMessagePayload = z.infer<typeof WebSocketMessagePayloadSchema>;
export type WebSocketRequestPayload = z.infer<typeof WebSocketRequestSchema>;

// Context Management Schemas
export const ContextItemSchema = z.object({
  id: z.string(),
  label: z.string(),
  value: z.string(),
});

export const ChatContextSchema = z.object({
  brands: z.array(ContextItemSchema).default([]),
  timePeriods: z.array(ContextItemSchema).default([]),
  metrics: z.array(ContextItemSchema).default([]),
  lastUpdated: z.number().default(() => Date.now()),
});

export type ContextItem = z.infer<typeof ContextItemSchema>;
export type ChatContext = z.infer<typeof ChatContextSchema>;

// Predefined context options
export const PREDEFINED_BRANDS: ContextItem[] = [
  { id: 'brand-a', label: 'Brand A', value: 'Brand A' },
  { id: 'brand-b', label: 'Brand B', value: 'Brand B' },
  { id: 'brand-c', label: 'Brand C', value: 'Brand C' },
  { id: 'brand-d', label: 'Brand D', value: 'Brand D' },
];

export const PREDEFINED_TIME_PERIODS: ContextItem[] = [
  { id: 'july-2024', label: 'July 2024', value: 'July 2024' },
  { id: 'august-2024', label: 'August 2024', value: 'August 2024' },
  { id: 'q1-2024', label: 'Q1 2024', value: 'Q1 2024' },
  { id: 'q2-2024', label: 'Q2 2024', value: 'Q2 2024' },
  { id: 'q3-2024', label: 'Q3 2024', value: 'Q3 2024' },
  { id: 'ytd-2024', label: 'YTD 2024', value: 'Year to Date 2024' },
];

export const PREDEFINED_METRICS: ContextItem[] = [
  { id: 'sales', label: 'Sales', value: 'Sales' },
  { id: 'revenue', label: 'Revenue', value: 'Revenue' },
  { id: 'growth', label: 'Growth', value: 'Growth' },
  { id: 'market-share', label: 'Market Share', value: 'Market Share' },
  { id: 'profit-margin', label: 'Profit Margin', value: 'Profit Margin' },
];

export function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

export function generateUserId(): string {
  return `user_${Math.random().toString(36).substring(2, 11)}`;
}
