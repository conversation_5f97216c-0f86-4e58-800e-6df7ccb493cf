'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { webSocketClient, ConnectionStatus } from './websocket-client';
import { Message, MessagePart, generateSessionId, generateUserId } from './schemas';

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  id?: string;
  parts?: MessagePart[];
}

export interface UseWebSocketChatOptions {
  body?: {
    userId?: string;
    sessionId?: string;
    userEmail?: string;
  };
}

export interface UseWebSocketChatReturn {
  messages: ChatMessage[];
  input: string;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  isLoading: boolean;
  connectionStatus: ConnectionStatus;
  error: string | null;
  retry: () => void;
  isAuthenticated: boolean;
  authError: string | null;
}

export function useWebSocketChat(_options: UseWebSocketChatOptions = {}): UseWebSocketChatReturn {
  const { data: session, status: sessionStatus } = useSession();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  const [error, setError] = useState<string | null>(null);
  const [authError, setAuthError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [sessionId] = useState(() => generateSessionId());

  const abortControllerRef = useRef<AbortController | null>(null);
  // Note: body options are available for future use (userId, sessionId, userEmail)
  // Currently WebSocket sends full message history, but these could be used for user context

  // Handle authentication and WebSocket connection
  useEffect(() => {
    const unsubscribeStatus = webSocketClient.onStatusChange((status) => {
      setConnectionStatus(status);
      if (status === 'connected') {
        setError(null);
        setAuthError(null);
      } else if (status === 'error') {
        setError('Connection failed. Please check your network and try again.');
      }
    });

    return () => {
      unsubscribeStatus();
    };
  }, []);

  // Handle session changes and authentication
  useEffect(() => {
    const handleAuthentication = async () => {
      if (sessionStatus === 'loading') {
        return; // Wait for session to load
      }

      if (sessionStatus === 'unauthenticated' || !session) {
        setIsAuthenticated(false);
        setAuthError('Authentication required');
        webSocketClient.setAuthToken(null);
        return;
      }

      if (sessionStatus === 'authenticated' && session) {
        try {
          // Get the JWT token from the session
          // In next-auth with JWT strategy, we need to get the token from the session
          const response = await fetch('/api/auth/session');
          const sessionData = await response.json();

          if (sessionData?.user) {
            setIsAuthenticated(true);
            setAuthError(null);

            // For JWT strategy, we need to get the actual JWT token
            // This is a simplified approach - in production you might want to
            // create a dedicated endpoint to get the JWT token for WebSocket use
            const tokenResponse = await fetch('/api/auth/token', {
              method: 'GET',
              credentials: 'include', // Pastikan cookies dikirim
              headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache'
              }
            });
            if (tokenResponse.ok) {
              const { token } = await tokenResponse.json();
              webSocketClient.setAuthToken(token);

              // Connect with authentication
              webSocketClient.connectWithAuth(token).catch((err) => {
                setAuthError(`Authentication failed: ${err.message}`);
                setError(err.message);
              });
            } else {
              // Fallback: connect without explicit token (rely on cookies)
              webSocketClient.connect().catch((err) => {
                setError(err.message);
              });
            }
          }
        } catch (err) {
          console.error('Authentication error:', err);
          setAuthError('Failed to authenticate WebSocket connection');

          // Fallback: try to connect without explicit token
          webSocketClient.connect().catch((connectErr) => {
            setError(connectErr.message);
          });
        }
      }
    };

    handleAuthentication();
  }, [session, sessionStatus]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setInput(e.target.value);
  }, []);

  const convertToWebSocketMessage = useCallback((chatMessage: ChatMessage): Message => {
    // Use existing parts if available, otherwise create text part from content
    const parts: MessagePart[] = chatMessage.parts || [
      {
        type: 'text',
        text: chatMessage.content,
      }
    ];

    return {
      role: chatMessage.role,
      content: chatMessage.content,
      parts,
    };
  }, []);

  const sendMessage = useCallback(async (userMessage: string) => {
    if (!userMessage.trim() || isLoading) return;

    setIsLoading(true);
    setError(null);

    // Create user message
    const userChatMessage: ChatMessage = {
      role: 'user',
      content: userMessage.trim(),
      id: `user_${Date.now()}`,
    };

    // Add user message to chat
    setMessages(prev => [...prev, userChatMessage]);

    try {
      // Prepare all messages for WebSocket (including conversation history)
      const allMessages = [...messages, userChatMessage];
      const webSocketMessages = allMessages.map(convertToWebSocketMessage);

      // Create abort controller for this request
      abortControllerRef.current = new AbortController();

      // Prepare user context
      const userContext = {
        userId: session?.user?.id || generateUserId(),
        email: session?.user?.email || '<EMAIL>',
        sessionId: sessionId,
      };

      // Send to WebSocket
      const response = await webSocketClient.sendMessage(webSocketMessages, userContext);

      // Handle response
      if (response.type === 'chat_response' && typeof response.payload === 'string') {
        const assistantMessage: ChatMessage = {
          role: 'assistant',
          content: response.payload,
          id: `assistant_${Date.now()}`,
        };

        setMessages(prev => [...prev, assistantMessage]);
      } else if (response.type === 'error') {
        const errorMessage = typeof response.payload === 'object'
          ? response.payload.error
          : 'An error occurred while processing your message.';
        setError(errorMessage);
      }

    } catch (err) {
      if (err instanceof Error && err.name !== 'AbortError') {
        setError(err.message || 'Failed to send message. Please try again.');
      }
    } finally {
      setIsLoading(false);
      abortControllerRef.current = null;
    }
  }, [messages, isLoading, convertToWebSocketMessage]);

  const handleSubmit = useCallback((e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!input.trim()) return;

    const messageToSend = input;
    setInput(''); // Clear input immediately
    sendMessage(messageToSend);
  }, [input, sendMessage]);

  const retry = useCallback(() => {
    setError(null);
    setAuthError(null);

    if (isAuthenticated && webSocketClient.getAuthToken()) {
      webSocketClient.reconnectWithAuth().catch((err) => {
        setError(err.message);
        setAuthError('Reconnection failed');
      });
    } else {
      webSocketClient.connect().catch((err) => {
        setError(err.message);
      });
    }
  }, [isAuthenticated]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    connectionStatus,
    error,
    retry,
    isAuthenticated,
    authError,
  };
}
